/**
 * 测试标签与纬度线平行的修复效果
 * 这个测试会在不同纬度创建标签，验证它们是否都保持水平
 */

import * as THREE from 'three';
import { SpherePoints } from './src/components/three/models/SpherePoints/SpherePoints.js';
import { FlightLine } from './src/components/three/models/SpherePoints/FlightLine.js';

// 创建测试场景
const scene = new THREE.Scene();
const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
const renderer = new THREE.WebGLRenderer();
renderer.setSize(window.innerWidth, window.innerHeight);
document.body.appendChild(renderer.domElement);

// 创建SpherePoints实例
const spherePoints = new SpherePoints(scene);
const flightLine = new FlightLine(spherePoints);

// 测试数据：不同纬度的点
const testPoints = [
  { lat: 0, lon: 0, name: "赤道" },      // 赤道
  { lat: 30, lon: 0, name: "北纬30°" },   // 北纬30度
  { lat: 60, lon: 0, name: "北纬60°" },   // 北纬60度
  { lat: 80, lon: 0, name: "北纬80°" },   // 北纬80度（接近北极）
  { lat: -30, lon: 0, name: "南纬30°" },  // 南纬30度
  { lat: -60, lon: 0, name: "南纬60°" },  // 南纬60度
  { lat: -80, lon: 0, name: "南纬80°" },  // 南纬80度（接近南极）
];

console.log("开始测试标签与纬度线平行修复...");

// 为每个测试点创建飞线和标签
testPoints.forEach((point, index) => {
  // 创建从赤道到目标点的飞线
  const startLat = 0;
  const startLon = index * 30; // 分散经度避免重叠
  const endLat = point.lat;
  const endLon = point.lon + index * 30;
  
  console.log(`创建测试点 ${index + 1}: ${point.name} (${endLat}°, ${endLon}°)`);
  
  // 创建飞线
  flightLine.createFlightLine(
    startLat, startLon,
    endLat, endLon,
    {
      // 线条配置
      lineOptions: {
        color: 0x00ff00,
        opacity: 0.8,
        segments: 50,
      },
      // 标签配置
      labelOptions: {
        show: true,
        text: point.name,
        fontSize: 24,
        color: "#ffffff",
        backgroundColor: "rgba(255, 0, 0, 0.8)",
        borderColor: "#ffffff",
        borderWidth: 2,
        padding: 8,
        scale: 1.0,
      },
      // 点标记配置
      pointOptions: {
        show: true,
        size: 0.02,
        color: 0xff0000,
      }
    }
  );
});

// 设置相机位置
camera.position.set(0, 0, 5);

// 渲染循环
function animate() {
  requestAnimationFrame(animate);
  
  // 缓慢旋转地球以观察不同角度的标签
  if (spherePoints.pointsGroup) {
    spherePoints.pointsGroup.rotation.y += 0.005;
  }
  
  renderer.render(scene, camera);
}

// 开始动画
animate();

// 添加控制台输出来验证修复效果
setTimeout(() => {
  console.log("=== 标签朝向验证 ===");
  
  // 检查所有标签的旋转角度
  spherePoints.pointsGroup.children.forEach((child) => {
    if (child.userData && child.userData.isTextLabel) {
      const rotation = child.rotation;
      console.log(`标签 "${child.userData.text}":`, {
        rotationX: (rotation.x * 180 / Math.PI).toFixed(1) + "°",
        rotationY: (rotation.y * 180 / Math.PI).toFixed(1) + "°", 
        rotationZ: (rotation.z * 180 / Math.PI).toFixed(1) + "°",
        position: {
          x: child.position.x.toFixed(2),
          y: child.position.y.toFixed(2),
          z: child.position.z.toFixed(2)
        }
      });
      
      // 验证标签是否水平（X和Z旋转应该接近0）
      const isHorizontal = Math.abs(rotation.x) < 0.1 && Math.abs(rotation.z) < 0.1;
      console.log(`  -> 是否水平: ${isHorizontal ? "✓" : "✗"}`);
    }
  });
}, 2000);

console.log("测试页面已加载，请观察标签是否在所有纬度都保持水平");
